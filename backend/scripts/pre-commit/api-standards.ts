#!/usr/bin/env bun

/**
 * API Standards Validation Script
 *
 * This script validates adherence to Buscafarma API standards including:
 * - Error handling patterns match centralized approach
 * - JWT authentication implementation consistency
 * - API response structure compliance
 * - Proper use of custom error classes
 */

import { execSync } from 'child_process'
import { existsSync, readFileSync } from 'fs'

interface APIStandardResult {
  success: boolean
  message: string
  details?: string[]
  severity?: 'low' | 'medium' | 'high'
}

interface APIStandardCheck {
  name: string
  description: string
  execute: () => APIStandardResult
}

/**
 * Gets list of staged TypeScript files
 */
function getStagedTSFiles(): string[] {
  try {
    const result = execSync('git diff --cached --name-only --diff-filter=ACM', { encoding: 'utf8' })
    return result
      .split('\n')
      .filter((file) => file.trim())
      .filter((file) => /\.ts$/.test(file))
      .filter((file) => existsSync(file))
      .filter((file) => !file.includes('test') && !file.includes('spec') && !file.includes('scripts/'))
  } catch {
    return []
  }
}

/**
 * Validates error handling patterns
 */
function errorHandlingPatternsCheck(): APIStandardResult {
  console.log('🔍 Checking error handling patterns...')

  const stagedFiles = getStagedTSFiles()
  if (stagedFiles.length === 0) {
    return { success: true, message: 'No TypeScript files to check for error handling' }
  }

  const issues: string[] = []

  for (const file of stagedFiles) {
    try {
      const content = readFileSync(file, 'utf8')

      // Check for proper custom error usage
      if (content.includes('throw new Error(') && !content.includes('test')) {
        // Check if custom errors are imported
        const hasCustomErrorImport =
          content.includes('AuthenticationError') ||
          content.includes('ValidationError') ||
          content.includes('ConflictError') ||
          content.includes('NotFoundError') ||
          content.includes('InternalServerError')

        if (!hasCustomErrorImport) {
          issues.push(`${file}: Use custom error classes instead of generic Error`)
        }
      }

      // Check for proper error handling in controllers
      if (file.includes('Controller') || file.includes('controller')) {
        if (content.includes('catch') && !content.includes('handleJWTError')) {
          const hasProperErrorHandling =
            content.includes('isCustomError') ||
            content.includes('getErrorStatusCode') ||
            content.includes('AuthenticationError') ||
            content.includes('ValidationError')

          if (!hasProperErrorHandling) {
            issues.push(`${file}: Controllers should use centralized error handling patterns`)
          }
        }
      }

      // Check for JWT error handling
      if (content.includes('jwt.verify') || content.includes('jwt.sign')) {
        if (!content.includes('handleJWTError') && !content.includes('try')) {
          issues.push(`${file}: JWT operations should use handleJWTError utility`)
        }
      }

      // Check for proper status code usage
      if (content.includes('set.status') && !content.includes('return {')) {
        const statusMatches = content.match(/set\.status\s*=\s*(\d+)/g)
        if (statusMatches) {
          for (const match of statusMatches) {
            const statusCode = match.match(/(\d+)/)?.[1]
            if (statusCode && !['200', '201', '400', '401', '404', '409', '500'].includes(statusCode)) {
              issues.push(`${file}: Use standard HTTP status codes (${statusCode} is non-standard)`)
            }
          }
        }
      }
    } catch (error) {
      issues.push(`${file}: Error reading file - ${error}`)
    }
  }

  if (issues.length > 0) {
    return {
      success: false,
      message: 'Error handling pattern violations detected',
      severity: 'medium',
      details: [
        'Please follow the established error handling patterns:',
        '• Use custom error classes (AuthenticationError, ValidationError, etc.)',
        '• Use handleJWTError for JWT-related errors',
        '• Follow centralized error handling in controllers',
        '',
        'Issues found:',
        ...issues
      ]
    }
  }

  console.log('✅ Error handling patterns are consistent')
  return { success: true, message: 'Error handling patterns validated' }
}

/**
 * Validates JWT authentication patterns
 */
function jwtAuthenticationPatternsCheck(): APIStandardResult {
  console.log('🔍 Checking JWT authentication patterns...')

  const stagedFiles = getStagedTSFiles()
  const issues: string[] = []

  for (const file of stagedFiles) {
    try {
      const content = readFileSync(file, 'utf8')

      // Check for proper token extraction
      if (content.includes('Bearer') && !content.includes('auth.slice(7)')) {
        if (content.includes('split') || content.includes('substring')) {
          issues.push(`${file}: Use consistent token extraction pattern (auth.slice(7))`)
        }
      }

      // Check for proper token validation
      if (content.includes('token') && content.includes('verify')) {
        // Skip validation for ProcessDataDeletionRequest.ts as it doesn't use tokens directly
        // Skip validation for EmailService.ts as it uses deletion tokens, not JWT tokens
        if (file.includes('ProcessDataDeletionRequest.ts') || file.includes('EmailService.ts')) {
          continue
        }

        if (!content.includes('validateTokenPresence') && !content.includes('if (!token)')) {
          issues.push(`${file}: Use validateTokenPresence utility for token validation`)
        }
      }

      // Check for proper JWT service usage
      if (content.includes('jwt.sign') || content.includes('jwt.verify')) {
        if (!content.includes('IJWT') && !content.includes('JWT')) {
          issues.push(`${file}: Use the JWT service interface for token operations`)
        }
      }

      // Check for proper authentication context
      if (file.includes('Router') || file.includes('router')) {
        if (content.includes('token') && !content.includes('derive')) {
          issues.push(`${file}: Use Elysia derive for token extraction in routers`)
        }
      }
    } catch (error) {
      issues.push(`${file}: Error reading file - ${error}`)
    }
  }

  if (issues.length > 0) {
    return {
      success: false,
      message: 'JWT authentication pattern violations detected',
      severity: 'medium',
      details: [
        'Please follow the established JWT patterns:',
        '• Use validateTokenPresence for token validation',
        '• Use auth.slice(7) for Bearer token extraction',
        '• Use the JWT service interface (IJWT)',
        '• Use Elysia derive for token extraction in routers',
        '',
        'Issues found:',
        ...issues
      ]
    }
  }

  console.log('✅ JWT authentication patterns are consistent')
  return { success: true, message: 'JWT authentication patterns validated' }
}

/**
 * Validates API response structure
 */
function apiResponseStructureCheck(): APIStandardResult {
  console.log('🔍 Checking API response structure...')

  const stagedFiles = getStagedTSFiles()
  const issues: string[] = []

  for (const file of stagedFiles) {
    try {
      const content = readFileSync(file, 'utf8')

      // Check for consistent response structure in controllers
      if (file.includes('Controller') || file.includes('controller')) {
        // Look for return statements
        const returnMatches = content.match(/return\s*\{[^}]*\}/g)
        if (returnMatches) {
          for (const returnMatch of returnMatches) {
            // Check if response has status
            if (!returnMatch.includes('status:')) {
              issues.push(`${file}: API responses should include status field`)
            }

            // Check for proper success response structure
            if (returnMatch.includes('status: 200') || returnMatch.includes('status: 201')) {
              if (!returnMatch.includes('data:') && !returnMatch.includes('message:')) {
                issues.push(`${file}: Success responses should include data or message field`)
              }
            }
          }
        }
      }

      // Check for proper error response structure in controllers only
      if (file.includes('Controller') || file.includes('controller')) {
        if (content.includes('onError') || (content.includes('return') && content.includes('error'))) {
          const errorResponses = content.match(/\{\s*status:\s*\d+,\s*message:/g)
          if (!errorResponses && content.includes('return') && content.includes('error')) {
            // Only flag if it's actually returning an error response, not just handling errors
            const returnErrorPattern = /return\s*\{[^}]*error[^}]*\}/g
            if (returnErrorPattern.test(content)) {
              issues.push(`${file}: Error responses should follow { status, message } structure`)
            }
          }
        }
      }

      // Check for pagination structure in controllers only
      if (file.includes('Controller') || file.includes('controller')) {
        if (content.includes('pagination') && content.includes('return')) {
          // Check if pagination is properly structured in return statements
          const paginationReturnPattern = /return\s*\{[^}]*pagination[^}]*\}/g
          if (paginationReturnPattern.test(content) && !content.includes('pagination:')) {
            const hasPaginationFields = content.includes('page') && content.includes('limit') && content.includes('total')
            if (!hasPaginationFields) {
              issues.push(`${file}: Paginated responses should include pagination metadata`)
            }
          }
        }
      }
    } catch (error) {
      issues.push(`${file}: Error reading file - ${error}`)
    }
  }

  if (issues.length > 0) {
    return {
      success: false,
      message: 'API response structure violations detected',
      severity: 'medium',
      details: [
        'Please follow the established response structure:',
        '• Success: { status, data } or { status, message }',
        '• Error: { status, message }',
        '• Paginated: { status, data: { items, pagination } }',
        '',
        'Issues found:',
        ...issues
      ]
    }
  }

  console.log('✅ API response structure is consistent')
  return { success: true, message: 'API response structure validated' }
}

/**
 * Main API standards validation function
 */
async function main() {
  console.log('📋 Running API Standards Validation\n')

  const checks: APIStandardCheck[] = [
    {
      name: 'Error Handling Patterns',
      description: 'Validating centralized error handling usage',
      execute: errorHandlingPatternsCheck
    },
    {
      name: 'JWT Authentication Patterns',
      description: 'Checking JWT implementation consistency',
      execute: jwtAuthenticationPatternsCheck
    },
    {
      name: 'API Response Structure',
      description: 'Validating response format compliance',
      execute: apiResponseStructureCheck
    }
  ]

  let allPassed = true
  const results: APIStandardResult[] = []

  for (const check of checks) {
    console.log(`\n📋 ${check.name}: ${check.description}`)
    const result = check.execute()
    results.push(result)

    if (!result.success) {
      allPassed = false
      const severityIcon = result.severity === 'high' ? '⚠️' : '⚡'

      console.error(`${severityIcon} ${check.name} failed: ${result.message}`)
      if (result.details) {
        result.details.forEach((detail) => console.error(`   ${detail}`))
      }
    }
  }

  console.log('\n📋 API Standards Summary:')
  results.forEach((result, index) => {
    const status = result.success ? '✅' : result.severity === 'high' ? '⚠️' : '⚡'
    console.log(`   ${status} ${checks[index].name}: ${result.message}`)
  })

  if (allPassed) {
    console.log('\n🎉 All API standards checks passed!')
    process.exit(0)
  } else {
    console.log('\n📋 API standards violations found. Please review and fix.')
    console.log('💡 Tip: Check the existing codebase for examples of proper patterns.')
    process.exit(1)
  }
}

// Run the API standards validation
main().catch((error) => {
  console.error('💥 Unexpected error during API standards check:', error)
  process.exit(1)
})
