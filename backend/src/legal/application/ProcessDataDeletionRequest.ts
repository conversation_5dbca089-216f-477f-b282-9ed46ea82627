import { ConflictError, NotFoundError, ValidationError, TokenExpiredError, TokenUsedError } from '../../server/errors/index.js'
import type { EmailService } from '../../services/EmailService.js'
import { TokenService } from '../../services/TokenService.js'
import { DataDeletionRequest } from '../domain/DataDeletionRequest.js'
import type { DataDeletionRepository } from '../infrastructure/repositories/DataDeletionRepository.js'

// Note: This class doesn't handle JWT tokens directly.
// Token validation is handled at the controller/router level.
// The validateTokenPresence utility is not needed here.

export interface DeletionRequestInput {
  email: string
  reason?: string
  ipAddress?: string
  userAgent?: string
}

export interface UserData {
  id: string
  name: string
  email: string
}

export class ProcessDataDeletionRequest {
  constructor(
    private readonly dataDeletionRepository: DataDeletionRepository,
    private readonly emailService: EmailService,
    private readonly tokenService: TokenService
  ) {}

  async createRequest(input: DeletionRequestInput, userData?: UserData): Promise<DataDeletionRequest> {
    // Validate input
    if (!input.email || !this.isValidEmail(input.email)) {
      throw new ValidationError('Valid email address is required')
    }

    // If user data is provided, verify email matches
    if (userData && userData.email !== input.email) {
      throw new ValidationError('Email does not match authenticated user')
    }

    // Rate limiting: Check for recent requests (max 3 per 24 hours)
    const recentRequestsCount = await this.dataDeletionRepository.countRecentRequestsByEmail(input.email, 24)
    if (recentRequestsCount >= 3) {
      throw new ConflictError('Too many deletion requests. Please wait 24 hours before requesting again.')
    }

    // Check for existing pending requests
    const existingRequest = await this.dataDeletionRepository.findPendingByEmail(input.email)
    if (existingRequest) {
      throw new ConflictError('A pending deletion request already exists for this email')
    }

    // Generate unique request ID
    const requestId = this.generateRequestId()

    // Generate secure deletion token
    const tokenData = await this.tokenService.generateDeletionToken(48) // 48 hours expiration

    // Create deletion request with token
    const userId = userData?.id || requestId // Use request ID as fallback

    const deletionRequest = DataDeletionRequest.createWithToken(
      requestId,
      userId,
      input.email,
      tokenData.token,
      tokenData.expiresAt,
      input.ipAddress,
      input.userAgent,
      input.reason
    )

    // Save to database
    const savedRequest = await this.dataDeletionRepository.create(deletionRequest)

    // Send confirmation email with deletion link
    try {
      await this.emailService.sendDeletionConfirmationWithLink(
        input.email,
        userData?.name || 'Usuario',
        requestId,
        tokenData.token,
        tokenData.expiresAt
      )
    } catch (_error) {
      // Don't fail the request if email fails, but log the error for debugging
    }

    // Send notification to admin (if configured)
    try {
      const adminEmail = process.env.ADMIN_EMAIL
      if (adminEmail) {
        await this.emailService.sendAdminDeletionNotification(adminEmail, input.email, requestId)
      }
    } catch (_error) {
      // Don't fail the request if email fails, but log the error for debugging
    }

    return savedRequest
  }

  async getRequestStatus(requestId: string): Promise<DataDeletionRequest> {
    const request = await this.dataDeletionRepository.findById(requestId)
    if (!request) {
      throw new NotFoundError('Deletion request not found')
    }
    return request
  }

  async getRequestsByEmail(email: string): Promise<DataDeletionRequest[]> {
    if (!this.isValidEmail(email)) {
      throw new ValidationError('Valid email address is required')
    }

    return this.dataDeletionRepository.findByEmail(email)
  }

  async confirmDeletion(token: string): Promise<DataDeletionRequest> {
    // Audit log: Deletion confirmation attempt
    console.log(`[AUDIT] Deletion confirmation attempt with token: ${token.substring(0, 8)}...`)

    // Find request by token
    const request = await this.dataDeletionRepository.findByToken(token)
    if (!request) {
      console.log(`[AUDIT] Invalid deletion token attempted: ${token.substring(0, 8)}...`)
      throw new NotFoundError('Invalid deletion token')
    }

    // Validate token
    if (request.isTokenExpired()) {
      console.log(`[AUDIT] Expired token used for deletion: ${request.id}, email: ${request.email}`)
      throw new TokenExpiredError('Deletion token has expired')
    }

    if (request.isTokenUsed()) {
      console.log(`[AUDIT] Already used token attempted for deletion: ${request.id}, email: ${request.email}`)
      throw new TokenUsedError('Deletion token has already been used')
    }

    if (!request.canConfirmDeletion()) {
      console.log(`[AUDIT] Invalid deletion confirmation attempt: ${request.id}, status: ${request.status}`)
      throw new ValidationError('Request cannot be confirmed at this time')
    }

    console.log(`[AUDIT] Valid deletion confirmation for request: ${request.id}, email: ${request.email}`)

    // Mark token as used and update status to processing
    const confirmedRequest = request.markTokenAsUsed().updateStatus('processing')
    const updatedRequest = await this.dataDeletionRepository.update(confirmedRequest)

    // Execute actual data deletion
    try {
      await this.executeUserDataDeletion(request.userId)

      // Mark as completed
      const completedRequest = updatedRequest.updateStatus('completed')
      const finalRequest = await this.dataDeletionRepository.update(completedRequest)

      // Send completion notification
      try {
        await this.emailService.sendDeletionCompletedNotification(
          request.email,
          'Usuario',
          request.id
        )
      } catch (_error) {
        // Don't fail if email fails
      }

      console.log(`[AUDIT] Account deletion completed successfully: ${request.id}, email: ${request.email}`)
      return finalRequest
    } catch (error) {
      // Mark as failed if deletion fails
      console.log(`[AUDIT] Account deletion failed: ${request.id}, email: ${request.email}, error: ${error}`)
      const failedRequest = updatedRequest.updateStatus('failed', `Deletion failed: ${error}`)
      await this.dataDeletionRepository.update(failedRequest)
      throw new ValidationError('Failed to complete account deletion')
    }
  }

  async processRequest(requestId: string, adminNotes?: string): Promise<DataDeletionRequest> {
    const request = await this.dataDeletionRepository.findById(requestId)
    if (!request) {
      throw new NotFoundError('Deletion request not found')
    }

    if (!request.isProcessable()) {
      throw new ValidationError('Request is not in a processable state')
    }

    // Update status to processing
    const processingRequest = request.updateStatus('processing', adminNotes)
    const updatedRequest = await this.dataDeletionRepository.update(processingRequest)

    // Here you would implement the actual data deletion logic
    // For now, we'll simulate the process

    return updatedRequest
  }

  async completeRequest(requestId: string, adminNotes?: string): Promise<DataDeletionRequest> {
    const request = await this.dataDeletionRepository.findById(requestId)
    if (!request) {
      throw new NotFoundError('Deletion request not found')
    }

    // Update status to completed
    const completedRequest = request.updateStatus('completed', adminNotes)
    const updatedRequest = await this.dataDeletionRepository.update(completedRequest)

    // Send completion notification to user
    try {
      await this.emailService.sendDeletionCompletedNotification(
        request.email,
        'Usuario', // We might not have the name anymore after deletion
        requestId
      )
    } catch (_error) {
      // Don't fail the completion if email fails
    }

    return updatedRequest
  }

  async cancelRequest(requestId: string, adminNotes?: string): Promise<DataDeletionRequest> {
    const request = await this.dataDeletionRepository.findById(requestId)
    if (!request) {
      throw new NotFoundError('Deletion request not found')
    }

    const cancelledRequest = request.updateStatus('cancelled', adminNotes)
    return this.dataDeletionRepository.update(cancelledRequest)
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  private generateRequestId(): string {
    // Generate a MongoDB-compatible ObjectID (24 hex characters)
    const timestamp = Math.floor(Date.now() / 1000)
      .toString(16)
      .padStart(8, '0')

    // Generate 16 random hex characters to complete the 24-character ObjectID
    let randomHex = ''
    for (let i = 0; i < 16; i++) {
      randomHex += Math.floor(Math.random() * 16).toString(16)
    }

    return `${timestamp}${randomHex}`
  }

  // Method to simulate actual user data deletion
  // This would be implemented based on your specific data deletion requirements
  async executeUserDataDeletion(_userId: string): Promise<void> {
    // TODO: Implement actual data deletion logic
    // This should include:
    // 1. Delete user record from database
    // 2. Delete medication favorites
    // 3. Invalidate authentication tokens
    // 4. Remove profile pictures from storage
    // 5. Anonymize or delete any other user-related data
    // 6. Keep audit logs and deletion request records for compliance
    // Example implementation:
    // await this.userRepository.deleteUser(userId)
    // await this.favoritesRepository.deleteByUserId(userId)
    // await this.authService.revokeAllTokens(userId)
    // await this.storageService.deleteUserFiles(userId)
  }
}
