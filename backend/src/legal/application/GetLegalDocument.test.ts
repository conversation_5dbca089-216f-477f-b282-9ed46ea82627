import { afterEach, beforeEach, describe, expect, it } from 'bun:test'
import { NotFoundError } from '../../server/errors/index.js'
import { LegalDocument } from '../domain/LegalDocument.js'
import { GetLegalDocument } from './GetLegalDocument.js'

describe('GetLegalDocument', () => {
  let getLegalDocument: GetLegalDocument

  beforeEach(() => {
    getLegalDocument = new GetLegalDocument()
  })

  afterEach(() => {
    // Cleanup after each test if needed
  })

  describe('execute', () => {
    it('should return privacy policy document', async () => {
      const document = await getLegalDocument.execute('privacy-policy')

      expect(document).toBeInstanceOf(LegalDocument)
      expect(document.id).toBe('privacy-policy-v2')
      expect(document.type).toBe('privacy-policy')
      expect(document.title).toBe('Política de Privacidad - BuscaFarma')
      expect(document.metadata.version).toBe('2.0')
      expect(document.metadata.language).toBe('es')
    })

    it('should return privacy policy with comprehensive content', async () => {
      const document = await getLegalDocument.execute('privacy-policy')

      expect(document.content).toContain('Información que recopilamos')
      expect(document.content).toContain('Cómo utilizamos tu información')
      expect(document.content).toContain('Compartir información')
      expect(document.content).toContain('Seguridad de los datos')
      expect(document.content).toContain('Tus derechos')
      expect(document.content).toContain('Retención de datos')
      expect(document.content).toContain('Cambios a esta política')
      expect(document.content).toContain('Contacto')
      expect(document.content).toContain('Menores de edad')
      expect(document.content).toContain('Transferencias internacionales')
    })

    it('should include BuscaFarma specific content', async () => {
      const document = await getLegalDocument.execute('privacy-policy')

      expect(document.content).toContain('BuscaFarma')
      expect(document.content).toContain('Firebase Authentication')
      expect(document.content).toContain('farmacias cercanas')
      expect(document.content).toContain('medicamentos favoritos')
      expect(document.content).toContain('<EMAIL>')
    })

    it('should include proper HTML structure in content', async () => {
      const document = await getLegalDocument.execute('privacy-policy')

      expect(document.content).toContain('<h2>')
      expect(document.content).toContain('<p>')
      expect(document.content).toContain('<ul>')
      expect(document.content).toContain('<li>')
      expect(document.content).toContain('<strong>')
    })

    it('should have recent last updated date', async () => {
      const document = await getLegalDocument.execute('privacy-policy')

      expect(document.metadata.lastUpdated).toBeInstanceOf(Date)
      expect(document.metadata.lastUpdated.getFullYear()).toBe(2024)
    })

    it('should return terms-of-service document', async () => {
      const document = await getLegalDocument.execute('terms-of-service')
      expect(document).toBeInstanceOf(LegalDocument)
      expect(document.type).toBe('terms-of-service')
    })

    it('should throw error for cookie-policy (not implemented)', async () => {
      await expect(getLegalDocument.execute('cookie-policy')).rejects.toThrow(NotFoundError)
      await expect(getLegalDocument.execute('cookie-policy')).rejects.toThrow('Cookie policy not implemented yet')
    })

    it('should throw error for unknown document type', async () => {
      await expect(getLegalDocument.execute('unknown-type' as any)).rejects.toThrow(NotFoundError)
      await expect(getLegalDocument.execute('unknown-type' as any)).rejects.toThrow('Unknown document type: unknown-type')
    })
  })

  describe('privacy policy content validation', () => {
    it('should include all required GDPR-like sections', async () => {
      const document = await getLegalDocument.execute('privacy-policy')
      const content = document.content

      // Data collection
      expect(content).toContain('Información de cuenta')
      expect(content).toContain('Información de ubicación')
      expect(content).toContain('Datos de salud')

      // Data usage
      expect(content).toContain('Proporcionar y mantener nuestro servicio')
      expect(content).toContain('Personalizar tu experiencia')

      // Data sharing
      expect(content).toContain('No vendemos, intercambiamos ni transferimos')

      // Security
      expect(content).toContain('medidas de seguridad apropiadas')
      expect(content).toContain('Encriptación de datos')

      // User rights
      expect(content).toContain('Acceder a tu información personal')
      expect(content).toContain('Eliminar tu cuenta')
      expect(content).toContain('Exportar tus datos')

      // Contact information
      expect(content).toContain('<EMAIL>')
    })

    it('should include mobile app specific considerations', async () => {
      const document = await getLegalDocument.execute('privacy-policy')
      const content = document.content

      expect(content).toContain('ubicación actual')
      expect(content).toContain('notificaciones')
      expect(content).toContain('aplicación')
    })
  })
})
