export type DeletionRequestStatus = 'pending' | 'pending_confirmation' | 'processing' | 'completed' | 'cancelled' | 'failed'

export interface DataDeletionMetadata {
  requestedAt: Date
  processedAt?: Date
  completedAt?: Date
  ipAddress?: string
  userAgent?: string
  reason?: string
}

export interface DataRetentionInfo {
  dataType: string
  willBeDeleted: boolean
  retentionPeriod?: string
  reason?: string
}

export class DataDeletionRequest {
  constructor(
    public readonly id: string,
    public readonly userId: string,
    public readonly email: string,
    public readonly status: DeletionRequestStatus,
    public readonly metadata: DataDeletionMetadata,
    public readonly dataRetentionInfo: DataRetentionInfo[] = [],
    public readonly adminNotes?: string,
    public readonly deletionToken?: string,
    public readonly tokenExpiresAt?: Date,
    public readonly tokenUsedAt?: Date
  ) {}

  static create(id: string, userId: string, email: string, ipAddress?: string, userAgent?: string, reason?: string): DataDeletionRequest {
    const metadata: DataDeletionMetadata = {
      requestedAt: new Date(),
      ipAddress,
      userAgent,
      reason
    }

    const defaultRetentionInfo: DataRetentionInfo[] = [
      {
        dataType: 'Personal Information (name, email)',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User account data'
      },
      {
        dataType: 'Location Data',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User preference data'
      },
      {
        dataType: 'Health Data & Preferences',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User health information'
      },
      {
        dataType: 'Medication Favorites',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User preference data'
      },
      {
        dataType: 'Authentication Tokens',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'Security tokens'
      },
      {
        dataType: 'Audit Logs',
        willBeDeleted: false,
        retentionPeriod: '7 years',
        reason: 'Legal compliance and fraud prevention'
      },
      {
        dataType: 'Deletion Request Record',
        willBeDeleted: false,
        retentionPeriod: '7 years',
        reason: 'Compliance with data protection regulations'
      }
    ]

    return new DataDeletionRequest(id, userId, email, 'pending_confirmation', metadata, defaultRetentionInfo)
  }

  static createWithToken(
    id: string,
    userId: string,
    email: string,
    deletionToken: string,
    tokenExpiresAt: Date,
    ipAddress?: string,
    userAgent?: string,
    reason?: string
  ): DataDeletionRequest {
    const metadata: DataDeletionMetadata = {
      requestedAt: new Date(),
      ipAddress,
      userAgent,
      reason
    }

    const defaultRetentionInfo: DataRetentionInfo[] = [
      {
        dataType: 'Personal Information (name, email)',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User account data'
      },
      {
        dataType: 'Location Data',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User preference data'
      },
      {
        dataType: 'Health Data & Preferences',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User health information'
      },
      {
        dataType: 'Medication Favorites',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User preference data'
      },
      {
        dataType: 'Authentication Tokens',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'Security tokens'
      },
      {
        dataType: 'Audit Logs',
        willBeDeleted: false,
        retentionPeriod: '7 years',
        reason: 'Legal compliance and fraud prevention'
      },
      {
        dataType: 'Deletion Request Record',
        willBeDeleted: false,
        retentionPeriod: '7 years',
        reason: 'Compliance with data protection regulations'
      }
    ]

    return new DataDeletionRequest(
      id,
      userId,
      email,
      'pending_confirmation',
      metadata,
      defaultRetentionInfo,
      undefined,
      deletionToken,
      tokenExpiresAt
    )
  }

  updateStatus(status: DeletionRequestStatus, adminNotes?: string): DataDeletionRequest {
    const updatedMetadata = { ...this.metadata }

    if (status === 'processing' && !updatedMetadata.processedAt) {
      updatedMetadata.processedAt = new Date()
    }

    if (status === 'completed' && !updatedMetadata.completedAt) {
      updatedMetadata.completedAt = new Date()
    }

    return new DataDeletionRequest(
      this.id,
      this.userId,
      this.email,
      status,
      updatedMetadata,
      this.dataRetentionInfo,
      adminNotes || this.adminNotes,
      this.deletionToken,
      this.tokenExpiresAt,
      this.tokenUsedAt
    )
  }

  markTokenAsUsed(): DataDeletionRequest {
    return new DataDeletionRequest(
      this.id,
      this.userId,
      this.email,
      this.status,
      this.metadata,
      this.dataRetentionInfo,
      this.adminNotes,
      this.deletionToken,
      this.tokenExpiresAt,
      new Date() // Set tokenUsedAt to current time
    )
  }

  isTokenExpired(): boolean {
    if (!this.tokenExpiresAt) return false
    return new Date() > this.tokenExpiresAt
  }

  isTokenUsed(): boolean {
    return !!this.tokenUsedAt
  }

  canConfirmDeletion(): boolean {
    return this.status === 'pending_confirmation' && !!this.deletionToken && !this.isTokenExpired() && !this.isTokenUsed()
  }

  isProcessable(): boolean {
    return this.status === 'pending' || this.status === 'pending_confirmation'
  }

  isCompleted(): boolean {
    return this.status === 'completed'
  }

  getEstimatedCompletionTime(): string {
    switch (this.status) {
      case 'pending':
        return 'Within 30 days'
      case 'pending_confirmation':
        return 'Awaiting email confirmation'
      case 'processing':
        return 'Within 14 days'
      case 'completed':
        return 'Completed'
      case 'cancelled':
        return 'Cancelled'
      case 'failed':
        return 'Failed - Contact support'
      default:
        return 'Unknown'
    }
  }

  toJSON() {
    return {
      id: this.id,
      userId: this.userId,
      email: this.email,
      status: this.status,
      metadata: {
        ...this.metadata,
        requestedAt: this.metadata.requestedAt.toISOString(),
        processedAt: this.metadata.processedAt?.toISOString(),
        completedAt: this.metadata.completedAt?.toISOString()
      },
      dataRetentionInfo: this.dataRetentionInfo,
      adminNotes: this.adminNotes,
      deletionToken: this.deletionToken,
      tokenExpiresAt: this.tokenExpiresAt?.toISOString(),
      tokenUsedAt: this.tokenUsedAt?.toISOString(),
      estimatedCompletion: this.getEstimatedCompletionTime()
    }
  }
}
