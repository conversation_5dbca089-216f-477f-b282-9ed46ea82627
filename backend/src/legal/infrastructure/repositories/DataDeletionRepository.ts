import { PrismaClient, data_deletion_request as PrismaDataDeletionRequest } from '@prisma/client'
import {
  type DataDeletionMetadata,
  DataDeletionRequest,
  type DataRetentionInfo,
  type DeletionRequestStatus
} from '../../domain/DataDeletionRequest.js'

export interface DataDeletionRepository {
  create(request: DataDeletionRequest): Promise<DataDeletionRequest>
  findById(id: string): Promise<DataDeletionRequest | null>
  findByEmail(email: string): Promise<DataDeletionRequest[]>
  findPendingByEmail(email: string): Promise<DataDeletionRequest | null>
  findByUserId(userId: string): Promise<DataDeletionRequest[]>
  update(request: DataDeletionRequest): Promise<DataDeletionRequest>
  findByStatus(status: DeletionRequestStatus): Promise<DataDeletionRequest[]>
  findPendingRequests(): Promise<DataDeletionRequest[]>
  findByToken(token: string): Promise<DataDeletionRequest | null>
  findPendingConfirmationByEmail(email: string): Promise<DataDeletionRequest | null>
  countRecentRequestsByEmail(email: string, hoursBack: number): Promise<number>
}

// Define the type for the database request with user relation
type DataDeletionRequestWithUser = PrismaDataDeletionRequest & {
  user: { id: string; name: string; email: string } | null
  deletion_token: string | null
  token_expires_at: Date | null
  token_used_at: Date | null
}

export class PrismaDataDeletionRepository implements DataDeletionRepository {
  constructor(private readonly prisma: PrismaClient) {}

  async create(request: DataDeletionRequest): Promise<DataDeletionRequest> {
    const data = {
      id: request.id,
      user_id: request.userId === request.id ? null : request.userId, // Set to null if using request ID as fallback
      email: request.email,
      status: request.status,
      deletion_token: request.deletionToken || null,
      token_expires_at: request.tokenExpiresAt || null,
      token_used_at: request.tokenUsedAt || null,
      requested_at: request.metadata.requestedAt,
      processed_at: request.metadata.processedAt || null,
      completed_at: request.metadata.completedAt || null,
      ip_address: request.metadata.ipAddress || null,
      user_agent: request.metadata.userAgent || null,
      reason: request.metadata.reason || null,
      data_retention_info: JSON.stringify(request.dataRetentionInfo),
      admin_notes: request.adminNotes || null
    }

    const created = await this.prisma.data_deletion_request.create({
      data,
      include: {
        user: true
      }
    })

    return this.mapToDomain(created as unknown as DataDeletionRequestWithUser)
  }

  async findById(id: string): Promise<DataDeletionRequest | null> {
    const request = await this.prisma.data_deletion_request.findUnique({
      where: { id },
      include: {
        user: true
      }
    })

    return request ? this.mapToDomain(request as unknown as DataDeletionRequestWithUser) : null
  }

  async findByEmail(email: string): Promise<DataDeletionRequest[]> {
    const requests = await this.prisma.data_deletion_request.findMany({
      where: { email },
      include: {
        user: true
      },
      orderBy: {
        requested_at: 'desc'
      }
    })

    return requests.map((request) => this.mapToDomain(request as unknown as DataDeletionRequestWithUser))
  }

  async findPendingByEmail(email: string): Promise<DataDeletionRequest | null> {
    const request = await this.prisma.data_deletion_request.findFirst({
      where: {
        email,
        status: {
          in: ['pending', 'pending_confirmation']
        }
      },
      include: {
        user: true
      }
    })

    return request ? this.mapToDomain(request as unknown as DataDeletionRequestWithUser) : null
  }

  async findByUserId(userId: string): Promise<DataDeletionRequest[]> {
    const requests = await this.prisma.data_deletion_request.findMany({
      where: { user_id: userId },
      include: {
        user: true
      },
      orderBy: {
        requested_at: 'desc'
      }
    })

    return requests.map((request) => this.mapToDomain(request as unknown as DataDeletionRequestWithUser))
  }

  async update(request: DataDeletionRequest): Promise<DataDeletionRequest> {
    const data = {
      status: request.status,
      deletion_token: request.deletionToken || null,
      token_expires_at: request.tokenExpiresAt || null,
      token_used_at: request.tokenUsedAt || null,
      processed_at: request.metadata.processedAt || null,
      completed_at: request.metadata.completedAt || null,
      data_retention_info: JSON.stringify(request.dataRetentionInfo),
      admin_notes: request.adminNotes || null,
      updated_at: new Date()
    }

    const updated = await this.prisma.data_deletion_request.update({
      where: { id: request.id },
      data,
      include: {
        user: true
      }
    })

    return this.mapToDomain(updated as unknown as DataDeletionRequestWithUser)
  }

  async findByStatus(status: DeletionRequestStatus): Promise<DataDeletionRequest[]> {
    const requests = await this.prisma.data_deletion_request.findMany({
      where: { status },
      include: {
        user: true
      },
      orderBy: {
        requested_at: 'desc'
      }
    })

    return requests.map((request) => this.mapToDomain(request as unknown as DataDeletionRequestWithUser))
  }

  async findPendingRequests(): Promise<DataDeletionRequest[]> {
    return this.findByStatus('pending')
  }

  async findByToken(token: string): Promise<DataDeletionRequest | null> {
    const request = await this.prisma.data_deletion_request.findFirst({
      where: {
        deletion_token: token
      },
      include: {
        user: true
      }
    })

    return request ? this.mapToDomain(request as unknown as DataDeletionRequestWithUser) : null
  }

  async findPendingConfirmationByEmail(email: string): Promise<DataDeletionRequest | null> {
    const request = await this.prisma.data_deletion_request.findFirst({
      where: {
        email,
        status: 'pending_confirmation'
      },
      include: {
        user: true
      }
    })

    return request ? this.mapToDomain(request as unknown as DataDeletionRequestWithUser) : null
  }

  async countRecentRequestsByEmail(email: string, hoursBack: number): Promise<number> {
    const cutoffDate = new Date()
    cutoffDate.setHours(cutoffDate.getHours() - hoursBack)

    return await this.prisma.data_deletion_request.count({
      where: {
        email,
        requested_at: {
          gte: cutoffDate
        }
      }
    })
  }

  private mapToDomain(dbRequest: DataDeletionRequestWithUser): DataDeletionRequest {
    const metadata: DataDeletionMetadata = {
      requestedAt: dbRequest.requested_at,
      processedAt: dbRequest.processed_at || undefined,
      completedAt: dbRequest.completed_at || undefined,
      ipAddress: dbRequest.ip_address || undefined,
      userAgent: dbRequest.user_agent || undefined,
      reason: dbRequest.reason || undefined
    }

    let dataRetentionInfo: DataRetentionInfo[] = []
    try {
      if (dbRequest.data_retention_info) {
        dataRetentionInfo = JSON.parse(
          typeof dbRequest.data_retention_info === 'string' ? dbRequest.data_retention_info : JSON.stringify(dbRequest.data_retention_info)
        )
      }
    } catch (error) {
      console.error('Failed to parse data retention info:', error)
      // Use default retention info if parsing fails
      dataRetentionInfo = this.getDefaultRetentionInfo()
    }

    return new DataDeletionRequest(
      dbRequest.id,
      dbRequest.user_id || dbRequest.id, // Use request ID if no user_id
      dbRequest.email,
      dbRequest.status as DeletionRequestStatus,
      metadata,
      dataRetentionInfo,
      dbRequest.admin_notes || undefined,
      dbRequest.deletion_token || undefined,
      dbRequest.token_expires_at || undefined,
      dbRequest.token_used_at || undefined
    )
  }

  private getDefaultRetentionInfo(): DataRetentionInfo[] {
    return [
      {
        dataType: 'Personal Information (name, email)',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User account data'
      },
      {
        dataType: 'Location Data',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User preference data'
      },
      {
        dataType: 'Health Data & Preferences',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User health information'
      },
      {
        dataType: 'Medication Favorites',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'User preference data'
      },
      {
        dataType: 'Authentication Tokens',
        willBeDeleted: true,
        retentionPeriod: 'Immediate',
        reason: 'Security tokens'
      },
      {
        dataType: 'Audit Logs',
        willBeDeleted: false,
        retentionPeriod: '7 years',
        reason: 'Legal compliance and fraud prevention'
      },
      {
        dataType: 'Deletion Request Record',
        willBeDeleted: false,
        retentionPeriod: '7 years',
        reason: 'Compliance with data protection regulations'
      }
    ]
  }
}
