import { execSync } from 'child_process'
import { existsSync, unlinkSync } from 'fs'
import { join } from 'path'
import { generateMockFirebaseToken, generateTestRefreshToken } from './auth.js'

// Test database configuration
const TEST_DB_PATH = join(process.cwd(), 'test.db')
const TEST_DATABASE_URL = `file:${TEST_DB_PATH}`

// Set test database URL before any Prisma operations
process.env.TEST_DATABASE_URL = TEST_DATABASE_URL

// Import test Prisma client after setting environment
let testPrisma: any = null

export const getTestPrismaClient = () => {
  if (!testPrisma) {
    // Dynamically import the test Prisma client using absolute path
    const path = require('path')
    const testClientPath = path.join(process.cwd(), 'node_modules/.prisma/test-client')
    const { PrismaClient } = require(testClientPath)
    testPrisma = new PrismaClient()
  }
  return testPrisma
}

export const setupTestDatabase = async (): Promise<void> => {
  try {
    // Remove existing test database
    if (existsSync(TEST_DB_PATH)) {
      unlinkSync(TEST_DB_PATH)
    }

    // Generate test Prisma client
    execSync('bun run test:db:generate', {
      stdio: 'inherit',
      env: { ...process.env, TEST_DATABASE_URL }
    })

    // Push schema to test database
    execSync('bun run test:db:push', {
      stdio: 'inherit',
      env: { ...process.env, TEST_DATABASE_URL }
    })

    console.log('✅ Test database setup completed')
  } catch (error) {
    console.error('❌ Test database setup failed:', error)
    throw error
  }
}

export const cleanupTestDatabase = async (): Promise<void> => {
  try {
    if (testPrisma) {
      await testPrisma.$disconnect()
    }

    // Remove test database file
    if (existsSync(TEST_DB_PATH)) {
      unlinkSync(TEST_DB_PATH)
    }

    console.log('✅ Test database cleanup completed')
  } catch (error) {
    console.error('❌ Test database cleanup failed:', error)
    throw error
  }
}

export const resetTestDatabase = async (): Promise<void> => {
  const prisma = getTestPrismaClient()

  try {
    // Delete all data in reverse order to handle foreign key constraints
    await prisma.medication_favorite.deleteMany()
    await prisma.data_deletion_request.deleteMany()
    await prisma.user.deleteMany()

    console.log('✅ Test database reset completed')
  } catch (error) {
    console.error('❌ Test database reset failed:', error)
    throw error
  }
}

export const seedTestDatabase = async (): Promise<void> => {
  const prisma = getTestPrismaClient()

  try {
    // Create test users
    const _testUser1 = await prisma.user.create({
      data: {
        id: 'test-user-1',
        name: 'Test User 1',
        email: '<EMAIL>',
        firebase_token: generateMockFirebaseToken('test-user-1'),
        current_location: JSON.stringify({ latitude: -12.0464, longitude: -77.0428 }),
        preferences: JSON.stringify({
          language: 'es',
          theme: 'light',
          notifications: true,
          defaultLocation: {
            departmentCode: '15',
            provinceCode: '01',
            districtCode: '01'
          }
        }),
        health_data: JSON.stringify({
          allergies: ['penicillin'],
          conditions: ['diabetes'],
          medications: [
            {
              name: 'Metformin',
              dosage: '500mg',
              frequency: 'twice daily'
            }
          ]
        }),
        profile_picture_url: 'https://example.com/avatar1.jpg',
        refreshToken: generateTestRefreshToken('test-user-1')
      }
    })

    const _testUser2 = await prisma.user.create({
      data: {
        id: 'test-user-2',
        name: 'Test User 2',
        email: '<EMAIL>',
        firebase_token: generateMockFirebaseToken('test-user-2'),
        current_location: JSON.stringify({ latitude: -12.0464, longitude: -77.0428 }),
        preferences: JSON.stringify({
          language: 'en',
          theme: 'dark',
          notifications: false
        }),
        health_data: JSON.stringify({
          allergies: [],
          conditions: [],
          medications: []
        }),
        refreshToken: generateTestRefreshToken('test-user-2')
      }
    })

    console.log('✅ Test database seeded with test users')
  } catch (error) {
    console.error('❌ Test database seeding failed:', error)
    throw error
  }
}
