import { randomBytes } from 'node:crypto'
import type { ITokenService, DeletionToken, TokenValidationResult } from './interfaces/ITokenService.js'

/**
 * Token Service for Secure Deletion Token Management
 * 
 * Provides cryptographically secure token generation and validation
 * for account deletion confirmation workflow.
 */
export class TokenService implements ITokenService {
  private readonly DEFAULT_TOKEN_LENGTH = 32 // 32 bytes = 256 bits
  private readonly DEFAULT_EXPIRATION_HOURS = 48 // 48 hours default expiration
  private readonly MIN_TOKEN_LENGTH = 16 // Minimum 16 bytes = 128 bits
  private readonly MAX_TOKEN_LENGTH = 64 // Maximum 64 bytes = 512 bits

  /**
   * Generate a cryptographically secure deletion token
   * @param expirationHours - Hours until token expires (default: 48)
   * @returns Promise containing token and expiration date
   */
  async generateDeletionToken(expirationHours: number = this.DEFAULT_EXPIRATION_HOURS): Promise<DeletionToken> {
    // Validate expiration hours
    if (expirationHours <= 0 || expirationHours > 168) { // Max 7 days
      throw new Error('Expiration hours must be between 1 and 168 (7 days)')
    }

    // Generate secure token
    const token = this.generateSecureToken()

    // Calculate expiration date
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + expirationHours)

    return {
      token,
      expiresAt
    }
  }

  /**
   * Validate a deletion token format and structure
   * @param token - The token to validate
   * @returns Promise containing validation result
   */
  async validateTokenFormat(token: string): Promise<TokenValidationResult> {
    // Check if token exists
    if (!token || typeof token !== 'string') {
      return {
        isValid: false,
        isExpired: false,
        isUsed: false,
        error: 'Token is required and must be a string'
      }
    }

    // Check token length (base64 encoded, so calculate original byte length)
    const expectedMinLength = Math.ceil((this.MIN_TOKEN_LENGTH * 4) / 3) // Base64 encoding
    const expectedMaxLength = Math.ceil((this.MAX_TOKEN_LENGTH * 4) / 3)

    if (token.length < expectedMinLength || token.length > expectedMaxLength) {
      return {
        isValid: false,
        isExpired: false,
        isUsed: false,
        error: `Token length must be between ${expectedMinLength} and ${expectedMaxLength} characters`
      }
    }

    // Check if token is valid base64url format
    const base64UrlRegex = /^[A-Za-z0-9_-]+$/
    if (!base64UrlRegex.test(token)) {
      return {
        isValid: false,
        isExpired: false,
        isUsed: false,
        error: 'Token must be valid base64url format'
      }
    }

    return {
      isValid: true,
      isExpired: false,
      isUsed: false
    }
  }

  /**
   * Check if a token has expired
   * @param expiresAt - Token expiration date
   * @returns Boolean indicating if token is expired
   */
  isTokenExpired(expiresAt: Date): boolean {
    return new Date() > expiresAt
  }

  /**
   * Generate a secure random token string
   * @param length - Token length in bytes (default: 32)
   * @returns URL-safe base64 encoded token string
   */
  generateSecureToken(length: number = this.DEFAULT_TOKEN_LENGTH): string {
    // Validate length
    if (length < this.MIN_TOKEN_LENGTH || length > this.MAX_TOKEN_LENGTH) {
      throw new Error(`Token length must be between ${this.MIN_TOKEN_LENGTH} and ${this.MAX_TOKEN_LENGTH} bytes`)
    }

    // Generate cryptographically secure random bytes
    const buffer = randomBytes(length)

    // Convert to URL-safe base64 (replace + with -, / with _, remove padding =)
    return buffer
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }
}
