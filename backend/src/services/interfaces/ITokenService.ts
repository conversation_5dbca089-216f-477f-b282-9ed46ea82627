/**
 * Token Service Interface for Secure Deletion Token Management
 *
 * Provides methods for generating, validating, and managing single-use
 * cryptographically secure tokens for account deletion confirmation.
 */

export interface DeletionToken {
  token: string
  expiresAt: Date
}

export interface TokenValidationResult {
  isValid: boolean
  isExpired: boolean
  isUsed: boolean
  error?: string
}

export interface ITokenService {
  /**
   * Generate a cryptographically secure deletion token
   * @param expirationHours - Hours until token expires (default: 48)
   * @returns Promise containing token and expiration date
   */
  generateDeletionToken(expirationHours?: number): Promise<DeletionToken>

  /**
   * Validate a deletion token format and structure
   * @param token - The token to validate
   * @returns Promise containing validation result
   */
  validateTokenFormat(token: string): Promise<TokenValidationResult>

  /**
   * Check if a token has expired
   * @param expiresAt - Token expiration date
   * @returns Boolean indicating if token is expired
   */
  isTokenExpired(expiresAt: Date): boolean

  /**
   * Generate a secure random token string
   * @param length - Token length in bytes (default: 32)
   * @returns URL-safe base64 encoded token string
   */
  generateSecureToken(length?: number): string
}
