/**
 * Custom Error Classes for Centralized Error Handling
 *
 * These error classes provide structured error handling with status codes
 * and consistent error messages across the entire application.
 */

export class AuthenticationError extends Error {
  public readonly statusCode: number = 401
  public readonly type: string = 'AUTHENTICATION_ERROR'

  constructor(message: string = 'Unauthorized') {
    super(message)
    this.name = 'AuthenticationError'

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AuthenticationError)
    }
  }
}

export class ValidationError extends Error {
  public readonly statusCode: number = 400
  public readonly type: string = 'VALIDATION_ERROR'

  constructor(message: string = 'Bad Request') {
    super(message)
    this.name = 'ValidationError'

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ValidationError)
    }
  }
}

export class ConflictError extends Error {
  public readonly statusCode: number = 409
  public readonly type: string = 'CONFLICT_ERROR'

  constructor(message: string = 'Conflict') {
    super(message)
    this.name = 'ConflictError'

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ConflictError)
    }
  }
}

export class NotFoundError extends Error {
  public readonly statusCode: number = 404
  public readonly type: string = 'NOT_FOUND_ERROR'

  constructor(message: string = 'Not Found') {
    super(message)
    this.name = 'NotFoundError'

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, NotFoundError)
    }
  }
}

export class InternalServerError extends Error {
  public readonly statusCode: number = 500
  public readonly type: string = 'INTERNAL_SERVER_ERROR'

  constructor(message: string = 'Internal server error') {
    super(message)
    this.name = 'InternalServerError'

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, InternalServerError)
    }
  }
}

export class TokenExpiredError extends Error {
  public readonly statusCode: number = 410
  public readonly type: string = 'TOKEN_EXPIRED_ERROR'

  constructor(message: string = 'Token has expired') {
    super(message)
    this.name = 'TokenExpiredError'

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, TokenExpiredError)
    }
  }
}

export class TokenUsedError extends Error {
  public readonly statusCode: number = 410
  public readonly type: string = 'TOKEN_USED_ERROR'

  constructor(message: string = 'Token has already been used') {
    super(message)
    this.name = 'TokenUsedError'

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, TokenUsedError)
    }
  }
}

/**
 * Type guard to check if an error is one of our custom errors
 */
export function isCustomError(error: unknown): error is AuthenticationError | ValidationError | ConflictError | NotFoundError | InternalServerError | TokenExpiredError | TokenUsedError {
  return (
    error instanceof AuthenticationError ||
    error instanceof ValidationError ||
    error instanceof ConflictError ||
    error instanceof NotFoundError ||
    error instanceof InternalServerError ||
    error instanceof TokenExpiredError ||
    error instanceof TokenUsedError
  )
}

/**
 * Helper function to get status code from any error
 */
export function getErrorStatusCode(error: unknown): number {
  if (isCustomError(error)) {
    return error.statusCode
  }
  return 500 // Default to internal server error
}

/**
 * Helper function to get error type from any error
 */
export function getErrorType(error: unknown): string {
  if (isCustomError(error)) {
    return error.type
  }
  return 'UNKNOWN_ERROR'
}
