/**
 * Error Handling Module Exports
 *
 * Barrel export file for all error handling utilities and classes.
 * Provides a single import point for error handling functionality.
 */

// Custom Error Classes
export {
  AuthenticationError,
  ValidationError,
  ConflictError,
  NotFoundError,
  InternalServerError,
  TokenExpiredError,
  TokenUsedError,
  isCustomError,
  getErrorStatusCode,
  getErrorType
} from './CustomErrors.js'

// JWT Error Handling Utilities
export {
  JWTErrorType,
  handleJWTError,
  isJWTError,
  getJWTErrorMessage,
  validateTokenPresence
} from './JWTErrorHandler.js'
