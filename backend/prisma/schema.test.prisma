// Test Prisma schema file for SQLite integration testing
// This mirrors the production MongoDB schema but uses SQLite for testing

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/test-client"
}

datasource db {
  provider = "sqlite"
  url      = env("TEST_DATABASE_URL")
}

model user {
  id                    String                @id @default(cuid())
  name                  String
  email                 String                @unique
  firebase_token        String
  current_location      String?               // JSON string for Point { latitude: number, longitude: number }
  preferences           String                // JSON string for UserPreferences
  health_data           String                // JSON string for HealthData
  profile_picture_url   String?
  refreshToken          String?               // JWT refresh token
  medication_favorites  medication_favorite[]
  data_deletion_requests data_deletion_request[]
  created_at            DateTime              @default(now())
  updated_at            DateTime              @updatedAt
}

model medication_favorite {
  id              String   @id @default(cuid())
  user_id         String
  user            user     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  // Medication identification fields for deduplication
  cod_estab       String   // Establishment code
  cod_prod_e      Int      // Product code

  // Complete medication data from MINSA API (stored as JSON string)
  medication_data String   // JSON string of full medication object

  // Metadata
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Compound indexes for efficient querying
  @@index([user_id, created_at])
  @@unique([user_id, cod_estab, cod_prod_e])
}

model data_deletion_request {
  id                String   @id @default(cuid())
  user_id           String?
  user              user?    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  // Request details
  email             String   // User email for verification
  status            String   // pending, pending_confirmation, processing, completed, cancelled, failed

  // Secure deletion token fields
  deletion_token    String?  @unique // Cryptographically secure single-use token
  token_expires_at  DateTime? // Token expiration timestamp
  token_used_at     DateTime? // When token was used for confirmation

  // Request metadata
  requested_at      DateTime @default(now())
  processed_at      DateTime?
  completed_at      DateTime?
  ip_address        String?
  user_agent        String?
  reason            String?

  // Data retention information (stored as JSON string)
  data_retention_info String   // JSON string of DataRetentionInfo objects array

  // Admin notes
  admin_notes       String?

  // Timestamps
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  // Indexes for efficient querying
  @@index([user_id, status])
  @@index([email, status])
  @@index([requested_at])
}
